const fs = require('fs');
const path = require('path');

const envPath = path.resolve(__dirname, '..', '.env.local');
if (!fs.existsSync(envPath)) {
  console.error('.env.local not found');
  process.exit(2);
}

const content = fs.readFileSync(envPath, 'utf8');

// Fail if common placeholder tokens remain
const placeholders = ['REPLACE_WITH', 'replace_with', 'REPLACE', 'EXAMPLE', 'your_', 'replace_me'];
const found = placeholders.filter(tok => content.includes(tok));
if (found.length) {
  console.error('Found placeholder tokens in .env.local:', found.join(', '));
  process.exit(1);
}

console.log('.env.local looks good (no placeholder tokens found)');
process.exit(0);
